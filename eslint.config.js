import nx from '@nx/eslint-plugin'
import reactHooks from 'eslint-plugin-react-hooks'
import sonarjs from 'eslint-plugin-sonarjs'
import react from 'eslint-plugin-react'
import tseslint from 'typescript-eslint'

export default [
  ...nx.configs['flat/base'],
  sonarjs.configs.recommended,
  reactHooks.configs['recommended-latest'],
  {
    plugins: { '@typescript-eslint': tseslint.plugin },
    languageOptions: { parser: tseslint.parser },
  },
  {
    ignores: [
      '**/dist',
      'node_modules',
      'apps/fleet-web/src/_third-party-libs-forks',
      '.nx/cache',
      'apps/fleet-web/scripts/gitlab/dist',
      'apps/fleet-web/src/cypress-ct/mocks',
      '**/vite.config.*.timestamp*',
      '**/vitest.config.*.timestamp*',
    ],
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    rules: {
      '@nx/enforce-module-boundaries': [
        'error',
        {
          // This is important because we want to use an alias to reference the project itself. Specially for apps.
          // For libs, we should add always add a banned export for the lib itself. Check how we do it for karoo-ui/core.
          allowCircularSelfDependency: true,
          enforceBuildableLibDependency: true,
          allow: ['assets/**'],
          depConstraints: [
            {
              sourceTag: 'scope:app',
              onlyDependOnLibsWithTags: ['scope:shared-lib'],
            },
            {
              sourceTag: 'scope:shared-lib',
              onlyDependOnLibsWithTags: ['scope:shared-lib'],
            },
          ],
        },
      ],
      'sonarjs/no-small-switch': 'off', // Up to the developer and reviewer to decide, not a lint rule.
      'sonarjs/max-switch-cases': ['error', 50],
      'sonarjs/no-nested-switch': 'off', // Up to the developer and reviewer to decide, not a lint rule.
      'sonarjs/cognitive-complexity': ['off'], // Looks good and interesting but we have way too many use cases where decent complexity still goes above the recommended threshold
      'sonarjs/no-identical-functions': ['error', 5], // 3 is the default but it's a bit too agressive. Duplication is not always bad.
      'sonarjs/prefer-immediate-return': 'off', // Doesn't always correlate to better readability.
      'sonarjs/no-duplicate-string': 'off',
      'sonarjs/no-nested-template-literals': 'off', // Does not provide a great benefit. And there are a lot of places with this right now.
      'sonarjs/prefer-single-boolean-return': 'off', // Doesn't always correlate to better readability.
      'sonarjs/no-collapsible-if': 'off', // Doesn't always provide a better readability. It's too extreme.
      'sonarjs/no-redundant-jump': 'off', // conflicts with TS noImplicitReturns (which is more worth it than this rule)
      'sonarjs/no-duplicated-branches': 'off', // forces a refactor way to early for simple and unharmful cases
      'sonarjs/redundant-type-aliases': 'off', // There are good use cases for it
      'sonarjs/no-empty-test-file': 'off',
      'sonarjs/no-fallthrough': 'off', // Typescript already has this covered
      'sonarjs/no-self-import': 'off', // oxlint already has this covered
      'sonarjs/rules-of-hooks': 'off', // react hooks rule is much better
      'sonarjs/no-hardcoded-passwords': 'off',
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/react-compiler': 'error',
    },
  },
  {
    files: ['**/*.jsx', '**/*.tsx'],
    plugins: { react },
    rules: {
      'react/jsx-key': [2, { checkFragmentShorthand: true, warnOnDuplicates: true }],
      'react/no-array-index-key': 'warn',
      'react/jsx-no-constructed-context-values': 'error',
      'react/no-danger': 'error',
      'react/no-unstable-nested-components': ['error', { allowAsProps: true }],
      'react/jsx-props-no-spread-multi': 'error',
      'react/forward-ref-uses-ref': 'error',
    },
  },
  {
    files: ['**/*.d.ts'],
    rules: {
      '@typescript-eslint/consistent-type-definitions': 'off',
    },
  },
]
