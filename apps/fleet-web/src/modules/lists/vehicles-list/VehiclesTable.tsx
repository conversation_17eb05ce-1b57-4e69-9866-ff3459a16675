import { useCallback, useMemo, useState } from 'react'
import { capitalize, isEqual, isNil, size } from 'lodash'
import {
  Autocomplete,
  Box,
  Chip,
  CircularProgress,
  DataGrid,
  Divider,
  GridActionsCellItem,
  LinearProgress,
  OverflowTypography,
  Skeleton,
  Stack,
  TextField,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridCellModesModel,
  type GridColDef,
  type GridEventListener,
  type GridRowModel,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { DateTime, Duration, type DurationObjectUnits } from 'luxon'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { DriverId, GPSFixType, VehicleId } from 'api/types'
import { getGeofences } from 'duxs/geofences-selector'
import { getCompanyName, getSettings } from 'duxs/user'
import {
  getDefaultVehiclesTableColumns,
  getStylePositionUnreliableTypeSetting,
  getUserPositionAddressStateGetter,
  getVehicleDetailsShowTerminalSerial,
  getVehiclesViewStatus,
} from 'duxs/user-sensitive-selectors'
import {
  getVehicleGroups,
  getVehicles,
  getVehiclesLoading,
  type ReduxVehicles,
} from 'duxs/vehicles'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import useUpdateVehicleSettingsMutation from 'src/modules/app/GlobalModals/VehicleDetails/VehicleSettings/useUpdateVehicleSettingsMutation'
import { UserFormattedLengthInKmOrMiles } from 'src/modules/components/connected'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import { DriverSingleSelect } from 'src/modules/shared/DriverSingleSelect'
import { DriverSingleWithGivenVehicleSelect } from 'src/modules/shared/DriverSingleWithGivenVehicleSelect'
import { useTypedSelector } from 'src/redux-hooks'
import { variables } from 'src/shared/components/styled/global-styles'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import Lookup from 'src/util-components/lookup'

import { ctIntl, FormattedDistance, StarRating, Stats } from 'cartrack-ui-kit'
import { useVehiclesCurrentGeofencesQuery } from '../useVehiclesCurrentGeofencesQuery'
import { getDriverName, type DriverMap } from '../utils'

const enum VehicleFilterOptions {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

type DataGridRow = ReduxVehicles[number]

const getStatusLabel = (statusName?: string) =>
  match(statusName)
    .with('no-signal', () => ({
      statusLabel: 'No Signal',
      color: 'white',
      textColor: 'black',
      chip: true,
    }))
    .with('no-signal ns-with-time', () => ({
      statusLabel: 'Last Seen',
      color: '',
      textColor: '',
      chip: false,
    }))
    .with('ignition-off', () => ({
      statusLabel: 'Ignition Off',
      color: 'white',
      textColor: 'black',
      chip: true,
    }))
    .with('idling', () => ({
      statusLabel: 'Idling',
      color: '#FF9800',
      textColor: 'white',
      chip: true,
    }))
    .with('driving', () => ({
      statusLabel: 'Driving',
      color: '#4CAF50',
      textColor: 'white',
      chip: true,
    }))
    .with('last-update', () => ({
      statusLabel: 'Last Update',
      color: '',
      textColor: '',
      chip: false,
    }))
    .with('stationary', () => ({
      statusLabel: 'Stationary',
      color: 'lightgray',
      textColor: '',
      chip: true,
    }))
    .otherwise(() => ({
      statusLabel: 'Status Unavailable',
      color: '',
      textColor: '',
      chip: false,
    }))

const basicInfoVehicleColumns = new Set<GridColDef<DataGridRow>['field']>([
  'registration',
  'statusClassName',
  'VIN',
  'odometer',
  'unitRawClock',
])

const vehiclesViewStatusColumns = new Set<GridColDef<DataGridRow>['field']>([
  'statusClassName',
  'speed',
  'homeGeofence',
  'currentGeofence',
  'positionDescription',
  'odometer',
])

export function VehiclesTable() {
  const history = useHistory()
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })
  const companyName = useTypedSelector(getCompanyName)
  const isSCDF = companyName === 'SCDF'
  const isSPF = companyName === 'SPF'

  const vehicleGroups = useTypedSelector(getVehicleGroups)
  const vehiclesLoading = useTypedSelector(getVehiclesLoading)
  const { vehicleOpenVehicle, vehicleBasicInfo } = useTypedSelector(getSettings)
  const vehiclesViewStatus = useTypedSelector(getVehiclesViewStatus)
  const stylePositionUnreliableType = useTypedSelector(
    getStylePositionUnreliableTypeSetting,
  )
  const getUserPositionAddressState = useTypedSelector(
    getUserPositionAddressStateGetter,
  )
  const defaultVehiclesTableColumns = useTypedSelector(getDefaultVehiclesTableColumns)
  const vehicleDetailsShowTerminalSerial = useTypedSelector(
    getVehicleDetailsShowTerminalSerial,
  )
  const vehicles = useTypedSelector(getVehicles)
  const geofencesList = useTypedSelector(getGeofences)
  const vehicleIds = useMemo(() => vehicles.map((v) => v.id), [vehicles])
  const driverQuery = useDriversQuery()

  const vehiclesCurrentGeofences = useVehiclesCurrentGeofencesQuery({ vehicleIds })

  const vehicleFilterOptions = useMemo(
    () => [
      {
        label: ctIntl.formatMessage({ id: 'All Vehicles' }),
        id: VehicleFilterOptions.ALL,
      },
      {
        label: ctIntl.formatMessage({ id: 'Active Vehicles' }),
        id: VehicleFilterOptions.ACTIVE,
      },
      {
        label: ctIntl.formatMessage({ id: 'Decommissioned Vehicles' }),
        id: VehicleFilterOptions.INACTIVE,
      },
    ],
    [],
  )

  // handle for set default driver for vehicle in cell mode
  const apiRef = useGridApiRef()
  const [cellModesModel, setCellModesModel] = useState<GridCellModesModel>({})
  const updateVehicleMutation = useUpdateVehicleSettingsMutation()
  const updateVehicleMutate = updateVehicleMutation.mutate

  const [vehicleStatusFilter, setVehicleStatusFilter] = useState<
    (typeof vehicleFilterOptions)[number]
  >(vehicleFilterOptions[0])

  const filteredVehicles = useMemo(
    () =>
      match(vehicleStatusFilter.id)
        .with(VehicleFilterOptions.ALL, () => vehicles)
        .with(VehicleFilterOptions.ACTIVE, () => vehicles.filter((v) => v.active))
        .with(VehicleFilterOptions.INACTIVE, () => vehicles.filter((v) => !v.active))
        .exhaustive(),
    [vehicleStatusFilter, vehicles],
  )

  const statusDurationToHuman = ({
    start,
    end,
  }: {
    start: DateTime
    end: DateTime
  }): string => {
    const duration = end.diff(start).shiftTo('days', 'hours', 'minutes').toObject()

    if ('days' in duration && duration.days && duration.days >= 20) {
      duration.minutes = 0
      duration.days = 0
    }

    if ('minutes' in duration && duration.minutes) {
      duration.minutes = Math.round(duration.minutes)
    }

    const cleanedDuration = Object.fromEntries(
      Object.entries(duration)
        .filter(([_key, value]) => value !== 0)
        .map(([key, value]) => [key, Math.abs(value)]),
    ) as DurationObjectUnits

    if (Object.keys(cleanedDuration).length === 0) {
      cleanedDuration.seconds = 0
    }

    return Duration.fromObject(cleanedDuration)
      .toHuman({ unitDisplay: 'short' })
      .replaceAll(/,\s/g, ' ')
  }

  const clockDurationToHuman = ({ rawClock }: { rawClock: number }): string => {
    const clockDuration = Duration.fromObject({
      seconds: rawClock,
    })
      .shiftTo('hours', 'minutes', 'seconds')
      .toObject()

    const cleanedDuration = Object.fromEntries(
      Object.entries(clockDuration)
        .filter(([_key, value]) => value !== 0)
        .map(([key, value]) => [key, Math.abs(value)]),
    ) as DurationObjectUnits

    if (Object.keys(cleanedDuration).length === 0) {
      cleanedDuration.seconds = 0
    }

    return Duration.fromObject(cleanedDuration)
      .toHuman({ unitDisplay: 'short' })
      .replaceAll(/,\s/g, ' ')
  }

  const showGpsUnreliableWarning = useCallback(
    (gpsFixType?: GPSFixType) => {
      if (gpsFixType === undefined) {
        return false
      }
      return (
        stylePositionUnreliableType === 'visible-warning' &&
        (isNil(gpsFixType) || gpsFixType < 3)
      )
    },
    [stylePositionUnreliableType],
  )

  // Update the vehicle with editable cell, currently only default driver
  const processRowUpdate = useCallback(
    async (newRow: GridRowModel<DataGridRow>, oldRow: GridRowModel<DataGridRow>) =>
      new Promise<DataGridRow>((resolve) => {
        const noChanges = isEqual(newRow, oldRow)
        const rollbackChanges = () => {
          resolve(oldRow)
        }

        if (noChanges) {
          rollbackChanges()
        } else {
          updateVehicleMutate(
            {
              vehicleId: newRow.id,
              data: {
                // update the default driver
                defaultDriver: newRow.defaultDriver ? newRow.defaultDriver : '',

                // keep the other values unchange
                defaultTimeZone: newRow.defaultTimeZone ?? '',
                monthlyMileageLimit: newRow.monthlyMileageLimit ?? '',
                type: newRow.type,
                iconColor: newRow.iconColor,
                homeGeofence: newRow.homeGeofence ? newRow.homeGeofence : '',
                tollingTagId: newRow.tollingTagId ?? '',
                maxSpeed: newRow.maxSpeed ?? '',
              },
              settingType: 'general',
            },
            {
              onSuccess: () => {
                resolve(newRow)
              },
              onError: () => rollbackChanges(),
            },
          )
        }
      }),
    [updateVehicleMutate],
  )

  const vehicleColumns = useMemo((): ReadonlyArray<GridColDef<DataGridRow>> => {
    const allDriversById = driverQuery.data?.allDriversById ?? (new Map() as DriverMap)

    const columns = [
      {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Name' }),
        field: 'name',
        valueGetter: (_, row) => row.name,
        minWidth: 200,
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={0.5}
            sx={{ minWidth: 0 }}
          >
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              {row.name}
            </OverflowTypography>
            {vehicleStatusFilter.id === VehicleFilterOptions.ALL && !row.active && (
              <Typography
                variant="inherit"
                color="text.secondary"
              >
                {ctIntl.formatMessage({ id: '(Discommisioned)' })}
              </Typography>
            )}
          </Stack>
        ),
      },
      columnHelper.string((_, row) => row.registration, {
        headerName: ctIntl.formatMessage({ id: 'Registration' }),
        field: 'registration',
        minWidth: 200,
      }),
      columnHelper.valueGetter(
        (_, row) => {
          const { statusClassName, lastIgnition } = row

          const dateHumanAbstraction = statusDurationToHuman({
            start: DateTime.fromJSDate(new Date(lastIgnition)),
            end: DateTime.now(),
          })

          const isStatusNever = statusClassName === 'ignition-off' && lastIgnition === 0

          const labelObject = getStatusLabel(
            isStatusNever ? undefined : statusClassName,
          )
          return ctIntl
            .formatMessage({
              id: labelObject.statusLabel,
            })
            .concat(' ', dateHumanAbstraction)
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Status' }),
          field: 'statusClassName',
          renderCell: ({ row }) => {
            const { statusClassName, lastIgnition, gpsFixType, lastTimestamp } = row

            const isStatusNever =
              statusClassName === 'ignition-off' && lastIgnition === 0

            const labelObject = getStatusLabel(
              isStatusNever ? undefined : statusClassName,
            )

            const dateHumanAbstraction = statusDurationToHuman({
              start: DateTime.fromJSDate(new Date(lastIgnition)),
              end: DateTime.now(),
            })

            return (
              <Tooltip
                title={
                  <Stack>
                    <Typography sx={{ fontSize: '10px' }}>
                      {`Vehicle has been "${labelObject.statusLabel}" for ${dateHumanAbstraction}`}
                    </Typography>
                    <Typography sx={{ fontSize: '10px' }}>
                      {`Last updated: ${DateTime.fromMillis(
                        lastTimestamp,
                      ).toLocaleString(DateTime.DATETIME_SHORT)}`}
                    </Typography>
                    {showGpsUnreliableWarning(gpsFixType) && (
                      <Typography sx={{ fontSize: '10px', fontWeight: 'bold' }}>
                        {ctIntl.formatMessage({ id: 'Location may be unreliable' })}
                      </Typography>
                    )}
                  </Stack>
                }
              >
                {labelObject.chip ? (
                  <Stack
                    direction="row"
                    gap={0.8}
                  >
                    <Chip
                      size="small"
                      sx={{
                        backgroundColor: labelObject.color,
                        border: `1px solid #BDBDBD`,
                        color: labelObject.textColor,
                      }}
                      label={ctIntl.formatMessage({ id: labelObject.statusLabel })}
                    />
                    {Boolean(lastIgnition) && (
                      <Typography
                        variant="inherit"
                        color="text.secondary"
                      >
                        {ctIntl.formatMessage({ id: 'for' })}
                      </Typography>
                    )}
                    {Boolean(lastIgnition) && (
                      <Typography
                        variant="inherit"
                        color="text.primary"
                      >
                        {dateHumanAbstraction}
                      </Typography>
                    )}
                  </Stack>
                ) : (
                  <Stack
                    direction="row"
                    gap={0.5}
                  >
                    <Typography
                      variant="inherit"
                      color="text.secondary"
                    >
                      {ctIntl.formatMessage({ id: labelObject.statusLabel })}
                    </Typography>
                    {!isStatusNever && (
                      <Typography
                        variant="inherit"
                        color="text.primary"
                      >
                        {dateHumanAbstraction}
                      </Typography>
                    )}
                    {!isStatusNever && (
                      <Typography
                        variant="inherit"
                        color="text.secondary"
                      >
                        {ctIntl.formatMessage({ id: 'ago' })}
                      </Typography>
                    )}
                  </Stack>
                )}
              </Tooltip>
            )
          },
          minWidth: 200,
        },
      ),
      columnHelper.string(
        (_, row) => {
          const driverName = row.driverName
          const driverId = row.defaultDriver as DriverId | null

          return getDriverName({
            allDriversById,
            driverId,
            driverNameObj: driverName,
          })
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          field: 'driverName',
          editable: true,
          renderCell: ({ value, row }) => (
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <OverflowTypography
                typographyProps={{
                  variant: 'inherit',
                  color:
                    value === ctIntl.formatMessage({ id: 'Unassigned' })
                      ? 'text.secondary'
                      : 'text.primary',
                }}
              >
                {value}
              </OverflowTypography>
              {!isSPF && (
                <Tooltip title={ctIntl.formatMessage({ id: 'Edit' })}>
                  <span>
                    <GridActionsCellItem
                      icon={<EditOutlinedIcon />}
                      label={ctIntl.formatMessage({ id: 'Edit' })}
                      onClick={() => {
                        apiRef.current?.startCellEditMode({
                          id: row.id,
                          field: 'driverName',
                        })
                      }}
                    />
                  </span>
                </Tooltip>
              )}
            </Box>
          ),
          // This is needed so that we get the most recent value on processRowUpdate
          valueSetter: (value, row) => ({ ...row, defaultDriver: value }),
          renderEditCell: ({ row, api }) => (
            <>
              {isSPF ? (
                <DriverSingleSelect
                  fullWidth
                  size="small"
                  onChange={(newValue) => {
                    api.setEditCellValue({
                      id: row.id,
                      field: 'driverName',
                      value: newValue ? newValue.value : null,
                    })
                  }}
                  driverId={row.defaultDriver as DriverId | null}
                  promptName={ctIntl.formatMessage({ id: 'Select Driver' })}
                />
              ) : (
                <DriverSingleWithGivenVehicleSelect
                  fullWidth
                  size="small"
                  onChange={(newValue) => {
                    api.setEditCellValue({
                      id: row.id,
                      field: 'driverName',
                      value: newValue ? newValue.value : null,
                    })
                  }}
                  vehicleId={row.id as VehicleId}
                  driverId={row.defaultDriver as DriverId | null}
                  promptName={ctIntl.formatMessage({ id: 'Select Driver' })}
                />
              )}
              <Tooltip title={ctIntl.formatMessage({ id: 'Save' })}>
                <span>
                  <GridActionsCellItem
                    icon={
                      updateVehicleMutation.isPending ? (
                        <CircularProgress size={20} />
                      ) : (
                        <CheckIcon />
                      )
                    }
                    label={ctIntl.formatMessage({ id: 'Save' })}
                    onClick={() => {
                      apiRef.current?.stopCellEditMode({
                        id: row.id,
                        field: 'driverName',
                      })
                    }}
                    material={{ sx: { color: 'success.main' } }}
                  />
                </span>
              </Tooltip>
              <Tooltip title={ctIntl.formatMessage({ id: 'Cancel' })}>
                <span>
                  <GridActionsCellItem
                    icon={<CloseIcon />}
                    label={ctIntl.formatMessage({ id: 'Cancel' })}
                    onClick={() => {
                      apiRef.current?.stopCellEditMode({
                        id: row.id,
                        field: 'driverName',
                        ignoreModifications: true,
                      })
                    }}
                    material={{ sx: { color: 'error.main' } }}
                  />
                </span>
              </Tooltip>
            </>
          ),
          minWidth: 250,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({
          id: isSCDF ? 'Vehicle Type' : 'Description',
        }),
        field: 'description',
      }),
      columnHelper.string((_, row) => row.description1, {
        headerName: ctIntl.formatMessage({
          id: isSCDF ? 'Vehicle Model' : 'Description 2',
        }),
        field: 'description1',
      }),
      columnHelper.string((_, row) => row.description2, {
        headerName: ctIntl.formatMessage({
          id: isSCDF ? 'Vehicle Usage' : 'Description 3',
        }),
        field: 'description2',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        field: 'rating',
        type: 'number',
        valueGetter: (_, row) => row.rating,
        renderCell: ({ row }) => (
          <StarRating
            color={'#FFB400'}
            rating={row.rating}
          />
        ),
        width: 80,
        headerAlign: 'left',
        align: 'left',
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Speed' }),
        field: 'speed',
        valueGetter: (_, row) => row.speed,
        renderCell: ({ row }) => (
          <OverflowTypography typographyProps={{ variant: 'inherit' }}>
            <FormattedDistance
              value={row.speed}
              perTime
              round
            />
          </OverflowTypography>
        ),
        maxWidth: 100,
      },
      columnHelper.valueGetter(
        (_, row) => {
          const possibleGeofence = geofencesList.find(
            (geofence) => geofence.id === row.homeGeofence,
          )
          if (possibleGeofence) {
            return possibleGeofence.name
          }
          return null
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Home Geofence' }),
          field: 'homeGeofence',
          renderCell: ({ row }) => (
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              <Lookup
                type="geofence"
                property="name"
                id={row.homeGeofence}
              />
            </OverflowTypography>
          ),
          minWidth: 150,
        },
      ),
      columnHelper.valueGetter(
        (_, row) =>
          match(vehiclesCurrentGeofences)
            .with({ status: 'pending' }, () => null)
            .with({ status: 'error' }, () => null)
            .with({ status: 'success' }, ({ data }) => data[row.id])
            .exhaustive(),
        {
          headerName: ctIntl.formatMessage({ id: 'Current Geofence' }),
          field: 'currentGeofence',
          renderCell: ({ row }) =>
            match(vehiclesCurrentGeofences)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="text"
                  sx={{ width: '100%', fontSize: '1rem' }}
                />
              ))
              .with({ status: 'error' }, () => null)
              .with({ status: 'success' }, ({ data }) => data[row.id])
              .exhaustive(),
          flex: 1,
          minWidth: 200,
        },
      ),
      {
        headerName: ctIntl.formatMessage({ id: 'Location' }),
        field: 'positionDescription',
        valueGetter: (_, row) =>
          match(
            getUserPositionAddressState({
              address: row.positionDescription,
              gpsFixType: row.gpsFixType,
            }),
          )
            .with('EMPTY', () => '')
            .with({ visibility: 'PRIVATE' }, () =>
              ctIntl.formatMessage({ id: 'Privacy Enabled' }),
            )
            .with(
              { visibility: 'PUBLIC' },
              ({ processedDescriptionText }) => processedDescriptionText,
            )
            .exhaustive(),
        renderCell: ({ row }) => (
          <UserFormattedPositionAddress
            address={row.positionDescription}
            gpsFixType={row.gpsFixType}
            statesRenderer={{
              publicAddress: ({ processedJSX, hasWarning }) => (
                <OverflowTypography
                  typographyProps={{
                    variant: 'body2',
                    sx: hasWarning
                      ? {
                          '& .MuiTypography-root': {
                            color: variables.red, // for consistency with warning color in UserFormattedPositionAddress
                          },
                          color: variables.red,
                        }
                      : {},
                  }}
                >
                  {processedJSX}
                </OverflowTypography>
              ),
            }}
          />
        ),
        flex: 1,
        minWidth: 300,
      },
      columnHelper.string((_, row) => row.VIN, {
        headerName: ctIntl.formatMessage({ id: 'VIN' }),
        field: 'VIN',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Odometer' }),
        field: 'odometer',
        type: 'number',
        valueGetter: (_, row) => {
          if (typeof row.odometer === 'number') {
            return Math.round(row.odometer)
          }
          return row.odometer
        },
        renderCell: ({ row }) => (
          <OverflowTypography typographyProps={{ variant: 'inherit' }}>
            {row.odometer ? (
              <UserFormattedLengthInKmOrMiles
                valueInKm={row.odometer}
                transformValueBeforeFormatting={Math.round}
              />
            ) : (
              <span>--</span>
            )}
          </OverflowTypography>
        ),
      },
      columnHelper.valueGetter(
        (_, row) =>
          isNil(row.unitRawClock)
            ? null
            : clockDurationToHuman({ rawClock: Number(row.unitRawClock) }),
        {
          headerName: ctIntl.formatMessage({ id: 'Clock' }),
          field: 'unitRawClock',
          renderCell: ({ row }) => (
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              {row.unitRawClock ? (
                clockDurationToHuman({ rawClock: Number(row.unitRawClock) })
              ) : (
                <span>--</span>
              )}
            </OverflowTypography>
          ),
        },
      ),
      ...(vehicleDetailsShowTerminalSerial
        ? [
            columnHelper.string((_, row) => row.terminalSerial, {
              headerName: ctIntl.formatMessage({ id: 'Terminal Serial' }),
              field: 'terminalSerial',
            }),
          ]
        : []),
      {
        headerName: ctIntl.formatMessage({ id: 'Defects' }),
        field: 'newDefectsCount',
        valueGetter: (_, row) => row.newDefectsCount,
        renderCell: ({ row }) =>
          row.newDefectsCount ? (
            <span>
              {ctIntl.formatMessage(
                { id: 'list.vehicles.table.defects.count' },
                { values: { count: row.newDefectsCount } },
              )}
            </span>
          ) : (
            <span>--</span>
          ),
      },
      // as const and satisfies are used to make sure we preserve the column `field` types
    ] as const satisfies ReadonlyArray<GridColDef<DataGridRow>>

    if (vehicleBasicInfo) {
      return columns.filter((c) => basicInfoVehicleColumns.has(c.field))
    } else if (!vehiclesViewStatus) {
      return columns.filter((c) => !vehiclesViewStatusColumns.has(c.field))
    } else {
      return columns
    }
  }, [
    apiRef,
    columnHelper,
    driverQuery.data?.allDriversById,
    geofencesList,
    getUserPositionAddressState,
    isSCDF,
    isSPF,
    showGpsUnreliableWarning,
    updateVehicleMutation.isPending,
    vehicleBasicInfo,
    vehicleDetailsShowTerminalSerial,
    vehicleStatusFilter.id,
    vehiclesCurrentGeofences,
    vehiclesViewStatus,
  ])

  const columnVisibilityModel: Record<GridColDef<DataGridRow>['field'], boolean> =
    useMemo(() => {
      if (defaultVehiclesTableColumns) {
        return vehicleColumns.reduce(
          (acc, c) => {
            if (!defaultVehiclesTableColumns.includes(c.field)) {
              acc[c.field] = false
            }
            return acc
          },
          {} as Record<GridColDef<DataGridRow>['field'], boolean>,
        )
      } else {
        return {
          defaultDriver: false,
          description: false,
          description1: false,
          description2: false,
          homeGeofence: false,
          VIN: false,
          odometer: false,
          unitRawClock: false,
          terminalSerial: false,
          newDefectsCount: false,
        } satisfies Record<GridColDef<DataGridRow>['field'], boolean>
      }
    }, [defaultVehiclesTableColumns, vehicleColumns])

  const handleCellEditStop = useCallback<GridEventListener<'cellEditStop'>>(
    (_, event) => {
      // eslint-disable-next-line no-param-reassign
      event.defaultMuiPrevented = true
    },
    [],
  )

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      dataGridId="sc-vehicles-list"
      data-testid="sc-vehicles-list"
      apiRef={apiRef}
      rows={filteredVehicles}
      columns={vehicleColumns}
      pagination
      pageSizeOptions={[25, 50, 100]}
      loading={vehiclesLoading}
      initialState={{
        columns: { columnVisibilityModel },
        pagination: { paginationModel: { pageSize: 25, page: 0 } },
      }}
      disableRowSelectionOnClick
      cellModesModel={cellModesModel}
      onCellModesModelChange={setCellModesModel}
      processRowUpdate={processRowUpdate}
      onCellEditStop={handleCellEditStop}
      onCellClick={(params, event) => {
        if (params.field === 'driverName') {
          event.stopPropagation()
        }
      }}
      onRowClick={({ row }) => {
        if (vehicleOpenVehicle) {
          history.push(getVehicleDetailsModalMainPath(history.location, row.id))
        }
      }}
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
        noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
          extraContent: {
            left: (
              <Autocomplete
                size="small"
                sx={{ width: '220px' }}
                options={vehicleFilterOptions}
                value={vehicleStatusFilter}
                getOptionLabel={(option) => option.label}
                disableClearable
                onChange={(_, value) => setVehicleStatusFilter(value)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={ctIntl.formatMessage({ id: 'Status Filter' })}
                  />
                )}
              />
            ),
            middle: (
              <Stack
                direction="row"
                gap={2}
              >
                <Stats
                  reversed
                  data={[
                    {
                      key: capitalize(vehicleStatusFilter.label),
                      value: size(filteredVehicles),
                    },
                  ]}
                />
                <Divider
                  orientation="vertical"
                  variant="middle"
                  flexItem
                />
                <Stats
                  reversed
                  data={[
                    {
                      key: `Total groups`,
                      value: size(vehicleGroups),
                    },
                  ]}
                />
              </Stack>
            ),
          },
        }),
        basePagination: { material: { showFirstButton: true, showLastButton: true } },
      }}
      sx={{ '& .MuiDataGrid-row': { cursor: 'pointer' } }}
    />
  )
}
