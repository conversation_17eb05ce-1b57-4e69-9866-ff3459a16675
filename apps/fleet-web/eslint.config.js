import { fileURLToPath } from 'node:url'
import path from 'node:path'
import { FlatCompat } from '@eslint/eslintrc'
// eslint-disable-next-line @nx/enforce-module-boundaries
import baseConfig from '../../eslint.config.js'
import vitest from '@vitest/eslint-plugin'
import eslintPluginNoOnlyTests from 'eslint-plugin-no-only-tests'
import pluginCypress from 'eslint-plugin-cypress/flat'
import eslintConfigPrettier from 'eslint-config-prettier/flat'

const __filename = fileURLToPath(import.meta.url)
const dirName = path.dirname(__filename)

const compat = new FlatCompat({ baseDirectory: dirName })

export default [
  ...baseConfig,
  ...compat.extends(
    'plugin:xss/recommended',
    'plugin:no-unsanitized/recommended-legacy',
  ),
  { plugins: { 'no-only-tests': eslintPluginNoOnlyTests } },
  {
    languageOptions: { globals: { ENV: 'readonly', google: 'readonly' } },
    linterOptions: { reportUnusedDisableDirectives: false },
  },
  ...compat.config({ plugins: ['xss', 'risxss'] }).map((config) => ({
    ...config,
    rules: {
      'xss/no-mixed-html': 'off',
      'xss/no-location-href-assign': ['error'],
      'risxss/catch-potential-xss-react': 'error',
    },
  })),
  {
    rules: {
      'sonarjs/aws-restricted-ip-admin-access': 'off',
      'sonarjs/use-type-alias': 'off',
      'sonarjs/fixme-tag': 'off',
      'sonarjs/alt-text': 'off',
      'sonarjs/no-redeclare': 'off',
      'sonarjs/pseudo-random': 'off',
      'sonarjs/no-unstable-nested-components': 'off',
      'sonarjs/jsx-no-useless-fragment': 'off',
      'sonarjs/no-commented-code': 'off',
      'sonarjs/no-nested-conditional': 'off',
      'sonarjs/todo-tag': 'off',
      'sonarjs/no-nested-functions': 'off',
      'sonarjs/public-static-readonly': 'off',
      'sonarjs/mouse-events-a11y': 'off',
      'sonarjs/assertions-in-tests': 'off',
      // When oxlint supports this rule, we can remove typescript eslint plugin. We really only need eslint for stuff that oxlint doesn't support and/or changes too fast, like react rules/compiler.
      '@typescript-eslint/no-restricted-types': [
        'error',
        {
          types: {
            Omit: {
              fixWith: 'Except',
              message:
                '`Omit` is generally unsafe because it does not check the properties you are trying to omit, exist.\nUse `Except` from `type-fest` instead (unless you have a __very__ specific reason to use `Omit`).\n\nReasons to still use `Omit`:\n - Preserve JS docs in some cases where `Except` removes it.\n - Spread of excess properties into omit.',
              suggest: ['Except'],
            },
            Extract: {
              fixWith: 'ExtractStrict',
              message:
                '`Extract` is generally unsafe because it does not check the properties you are trying to extract, exist.\nUse `ExtractStrict` instead (unless you have a __very__ specific reason to use `Extract`).',
              suggest: ['ExtractStrict'],
            },
            Exclude: {
              fixWith: 'ExcludeStrict',
              message:
                '`Exclude` is generally unsafe because it does not check the types you are trying to exclude, exist.\nUse `ExcludeStrict` instead (unless you have a __very__ specific reason to use `Exclude`).',
              suggest: ['ExcludeStrict'],
            },
          },
        },
      ],
      'no-loss-of-precision': 'off',
      'one-var': ['error', 'never'],
      'no-template-curly-in-string': 'off',
      'no-restricted-syntax': [
        'error',
        {
          selector:
            "CallExpression[callee.object.name='Array'][callee.property.name='isArray']",
          message: 'Avoid using Array.isArray. Use R.isArray from remeda instead',
        },
      ],
      'no-unsanitized/property': [
        'error',
        {
          escape: {
            methods: ['DOMPurify.sanitize'],
          },
        },
      ],
      'eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          ignoreRestSiblings: true,
          argsIgnorePattern: '^_',
        },
      ],
      'prefer-const': [
        'error',
        {
          destructuring: 'all',
        },
      ],
      'no-console': [
        'error',
        {
          allow: ['info', 'warn', 'error'],
        },
      ],
      'arrow-body-style': ['error', 'as-needed'],
      'no-param-reassign': [
        'error',
        {
          props: true,
          ignorePropertyModificationsFor: ['acc', 'draft'],
        },
      ],
      'jsx-a11y/anchor-has-content': 'off',
      'jsx-a11y/alt-text': 'off',
      'import/first': 'off',
      'react/jsx-no-useless-fragment': 'off',
      'array-callback-return': 'off',
      'react-hooks/exhaustive-deps': [
        'error',
        {
          additionalHooks:
            '(useEffectExceptOnMount|useDeepCompareMemo|useCallbackBranded|useMemoBranded)',
        },
      ],
    },
  },
  ...compat
    .config({
      plugins: ['@jambit/typed-redux-saga'],
    })
    .map((config) => ({
      ...config,
      files: ['**/*.ts', '**/*.js'],
      rules: {
        '@jambit/typed-redux-saga/use-typed-effects': ['error', 'default'],
        '@jambit/typed-redux-saga/delegate-effects': 'error',
      },
      ignores: ['**/*.spec.ts'],
    })),
  {
    files: ['**/*.spec.js', '**/*.spec.jsx', '**/*.spec.ts', '**/*.spec.tsx'],
    plugins: { vitest },
    rules: {
      ...vitest.configs.recommended.rules,
      'no-restricted-globals': [
        'error',
        'expect',
        'test',
        'describe',
        'it',
        'beforeEach',
        'afterEach',
        'beforeAll',
        'afterAll',
      ],
      'vitest/expect-expect': [
        'error',
        {
          assertFunctionNames: ['expect', 'vExpect'],
        },
      ],
    },
  },
  {
    ...pluginCypress.configs.recommended,
    files: ['**/*.cy.js', '**/*.cy.jsx', '**/*.cy.ts', '**/*.cy.tsx'],
    rules: {
      ...pluginCypress.configs.recommended.rules,
      'no-only-tests/no-only-tests': 'error',
    },
  },
  eslintConfigPrettier,
]
