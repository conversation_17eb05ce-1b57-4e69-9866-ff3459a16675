{
  "name": "fleet-web",
  "$schema": "../../node_modules/nx/schemas/project-schema.json",
  "sourceRoot": "apps/fleet-web/src",
  "projectType": "application",
  "targets": {
    "build": {
      "executor": "nx:run-commands",
      "options": {
        "command": "rimraf ./dist/apps/fleet-web && pnpm cross-env NODE_OPTIONS=--max-old-space-size=4144 NODE_ENV=production rspack build --config ./apps/fleet-web/webpack.config.cjs"
      }
    },
    "serve": {
      "executor": "nx:run-commands",
      "options": {
        "command": "cross-env NODE_OPTIONS=--max-old-space-size=4144 NODE_ENV=development rspack serve --config ./apps/fleet-web/webpack.config.cjs",
        "envFile": "apps/fleet-web/.env"
      }
    },
    "docker-serve": {
      "executor": "nx:run-commands",
      "options": {
        "command": "cross-env NODE_OPTIONS=--max-old-space-size=4144 NODE_ENV=development DEVELOPMENT_ENV=docker rspack serve --config ./apps/fleet-web/webpack.config.cjs",
        "envFile": "apps/fleet-web/.env"
      }
    },
    "lint": {
      "executor": "nx:run-commands",
      "options": {
        "command": "oxlint -c .oxlintrc.json --tsconfig=apps/fleet-web/tsconfig.app.json && pnpm eslint --config apps/fleet-web/eslint.config.js apps/fleet-web/src"
      }
    },
    "lint-ci": {
      "executor": "nx:run-commands",
      "options": {
        "command": "oxlint -c .oxlintrc.json --tsconfig=apps/fleet-web/tsconfig.app.json && pnpm eslint --config apps/fleet-web/eslint.config.js apps/fleet-web/src"
      }
    },
    "test": {
      "executor": "nx:run-commands",
      "options": {
        "command": "NODE_ENV=development vitest -c apps/fleet-web/vite.config.ts"
      }
    },
    "component-test": {
      "executor": "nx:run-commands",
      "options": {
        // Using env-cmd here to make sure that ONLY the cypress-ct env vars are used. When using NX "envFile" option, it will use the .env file as well (which is not what we want).
        "command": "env-cmd -f apps/fleet-web/.env.cypress-ct pnpm cross-env NODE_ENV=cypress-component-test-run CYPRESS_INTERNAL_BROWSER_CONNECT_TIMEOUT=300000 cypress run --component --config-file ./apps/fleet-web/cypress.config.cjs"
      }
    },
    "component-test-ci-chunk1": {
      "executor": "nx:run-commands",
      "options": {
        "command": "nx run fleet-web:component-test --spec='apps/fleet-web/**/*,!apps/fleet-web/src/modules/reports,!apps/fleet-web/src/modules/lists/Geofences,!apps/fleet-web/src/modules/admin/user-settings,!apps/fleet-web/src/modules/admin/APISettings,!apps/fleet-web/src/modules/admin/CustomForms,!apps/fleet-web/src/modules/vision,!apps/fleet-web/src/modules/know-the-driver,!apps/fleet-web/src/modules/ruc,!apps/fleet-web/src/modules/admin/reminders,!apps/fleet-web/src/modules/coaching,!apps/fleet-web/src/modules/vision,!apps/fleet-web/src/modules/map-view/VehicleCluster'"
      }
    },
    "component-test-ci-chunk2": {
      "executor": "nx:run-commands",
      "options": {
        "command": "nx run fleet-web:component-test --spec='apps/fleet-web/src/modules/lists/Geofences,apps/fleet-web/src/modules/admin/user-settings,apps/fleet-web/src/modules/admin/APISettings'"
      }
    },
    "component-test-ci-chunk3": {
      "executor": "nx:run-commands",
      "options": {
        "command": "nx run fleet-web:component-test --spec='apps/fleet-web/src/modules/reports'"
      }
    },
    "component-test-parallel": {
      "executor": "nx:run-commands",
      "options": {
        "command": "cypress-parallel -s fleet-web-component-test-run -t 4 -d 'apps/fleet-web/**/*.cy.{tsx,jsx,js,ts}'"
      }
    },
    "component-test-open": {
      "executor": "nx:run-commands",
      "options": {
        // Using env-cmd here to make sure that ONLY the cypress-ct env vars are used. When using NX "envFile" option, it will use the .env file as well (which is not what we want).
        "command": "env-cmd -f apps/fleet-web/.env.cypress-ct pnpm cross-env NODE_ENV=cypress-component-test-open NODE_OPTIONS=--max_old_space_size=4144 cypress open --component -b chrome --config-file ./apps/fleet-web/cypress.config.cjs"
      }
    },
    "tsc-check": {
      "executor": "nx:run-commands",
      "options": {
        "command": "cross-env NODE_OPTIONS=--max-old-space-size=5800 tsc -p ./apps/fleet-web/tsconfig.app.json && pnpm tsc -p ./apps/fleet-web/cypress/tsconfig.cy.json"
      }
    },
    "tsgo-check": {
      "executor": "nx:run-commands",
      "options": {
        "command": "GOGC=15 GOMEMLIMIT=2500MiB cross-env tsgo -p apps/fleet-web/tsconfig.app.json --singleThreaded && pnpm tsgo -p ./apps/fleet-web/cypress/tsconfig.cy.json"
      }
    },
    "update-locales": {
      "executor": "nx:run-commands",
      "options": {
        "command": "pnpx bun run apps/fleet-web/scripts/translate.ts"
      }
    },
    "build-gitlab-hooks": {
      "executor": "nx:run-commands",
      "options": {
        "command": "pnpx bun build apps/fleet-web/scripts/gitlab/merge_request_event_hooks.ts --outdir apps/fleet-web/scripts/gitlab/dist && pnpx bun build apps/fleet-web/scripts/gitlab/on_development_branch_push.ts --outdir apps/fleet-web/scripts/gitlab/dist"
      }
    }
  },
  "tags": [],
  "namedInputs": {
    "projectSpecificFiles": [
      "{workspaceRoot}/dist/libs/karoo-ui/**/*",
      "{workspaceRoot}/dist/libs/karoo-utils/**/*"
    ]
  }
}
